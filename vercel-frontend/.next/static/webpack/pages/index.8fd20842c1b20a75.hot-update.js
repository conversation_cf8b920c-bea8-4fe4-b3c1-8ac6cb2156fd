"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/FileUpload */ \"./components/FileUpload.tsx\");\n/* harmony import */ var _components_ProjectionList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ProjectionList */ \"./components/ProjectionList.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Header */ \"./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Footer */ \"./components/Footer.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [refreshList, setRefreshList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleUploadSuccess = ()=>{\n        // 刷新投影列表\n        setRefreshList((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Neptunium - Minecraft投影系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Minecraft建筑投影上传和管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children:  true && [\n                            ...Array(50)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white/20 rounded-full\",\n                                initial: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight\n                                },\n                                animate: {\n                                    x: Math.random() * window.innerWidth,\n                                    y: Math.random() * window.innerHeight\n                                },\n                                transition: {\n                                    duration: Math.random() * 20 + 10,\n                                    repeat: Infinity,\n                                    repeatType: \"reverse\"\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 pt-24 pb-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"text-center mb-12\",\n                                initial: {\n                                    opacity: 0,\n                                    y: -50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h1, {\n                                        className: \"text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-6\",\n                                        initial: {\n                                            scale: 0.5\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        children: \"\\uD83C\\uDFD7️ Neptunium\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                        className: \"text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            \"Minecraft建筑投影系统 - 让你的创意在游戏中绽放\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: \"支持多种建筑文件格式，一键部署到游戏世界\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: \"flex flex-wrap justify-center gap-4 text-sm\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            \"✨ 支持Litematica\",\n                                            \"\\uD83D\\uDD27 支持WorldEdit\",\n                                            \"\\uD83D\\uDDBC️ 支持图片预览\",\n                                            \"☁️ 云端存储\"\n                                        ].map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.span, {\n                                                className: \"backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-white hover:bg-white/20 transition-all duration-300\",\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    delay: 0.8 + index * 0.1\n                                                },\n                                                children: tag\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: \"backdrop-blur-xl bg-white/10 rounded-2xl border border-white/20 shadow-2xl p-8 hover:bg-white/15 transition-all duration-300\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -100\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        whileHover: {\n                                            scale: 1.02,\n                                            boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-semibold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-4xl\",\n                                                        children: \"�\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"上传建筑文件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onUploadSuccess: handleUploadSuccess\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: \"backdrop-blur-xl bg-white/10 rounded-2xl border border-white/20 shadow-2xl p-8 hover:bg-white/15 transition-all duration-300\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 100\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.8\n                                        },\n                                        whileHover: {\n                                            scale: 1.02,\n                                            boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-semibold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-4xl\",\n                                                        children: \"\\uD83C\\uDFD7️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"我的投影\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectionList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, refreshList, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 100\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 1.0\n                                },\n                                children: [\n                                    {\n                                        icon: \"⚡\",\n                                        title: \"极速部署\",\n                                        desc: \"一键上传，秒级部署到游戏世界\"\n                                    },\n                                    {\n                                        icon: \"\\uD83D\\uDD12\",\n                                        title: \"安全可靠\",\n                                        desc: \"企业级安全保障，数据加密存储\"\n                                    },\n                                    {\n                                        icon: \"\\uD83C\\uDF10\",\n                                        title: \"云端同步\",\n                                        desc: \"多设备同步，随时随地管理投影\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: \"backdrop-blur-xl bg-white/5 rounded-xl border border-white/10 p-6 text-center hover:bg-white/10 transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 1.2 + index * 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: feature.desc\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"HEyIB/UOVQSqIOE4GKZ+EGwvUNE=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});