"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Menu,X!=!lucide-react */ \"__barrel_optimize__?names=ExternalLink,Github,Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Header() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n        className: \"fixed top-0 left-0 right-0 backdrop-blur-xl bg-white/5 border-b border-white/10 z-50\",\n        initial: {\n            opacity: 0,\n            y: -50\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"flex items-center space-x-2\",\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"text-2xl\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        type: \"spring\",\n                                        stiffness: 300\n                                    },\n                                    children: \"\\uD83C\\uDFD7️\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                            children: \"Neptunium\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minecraft投影系统\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.nav, {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            children: [\n                                [\n                                    {\n                                        name: \"上传文件\",\n                                        href: \"#upload\"\n                                    },\n                                    {\n                                        name: \"投影列表\",\n                                        href: \"#projections\"\n                                    },\n                                    {\n                                        name: \"使用文档\",\n                                        href: \"#docs\"\n                                    }\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                        href: item.href,\n                                        className: \"text-gray-300 hover:text-blue-400 transition-colors px-3 py-2 rounded-lg hover:bg-white/10\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6 + index * 0.1\n                                        },\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                    href: \"https://github.com/Mcxiaocaibug/neptunium-frontend\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-blue-400 transition-colors px-3 py-2 rounded-lg hover:bg-white/10\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.9\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Github, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 text-gray-300 hover:text-blue-400 transition-colors rounded-lg hover:bg-white/10\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 27\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Menu, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 55\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                    children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"md:hidden border-t border-white/10 backdrop-blur-xl bg-white/5\",\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"py-4 space-y-2\",\n                            children: [\n                                [\n                                    {\n                                        name: \"上传文件\",\n                                        href: \"#upload\"\n                                    },\n                                    {\n                                        name: \"投影列表\",\n                                        href: \"#projections\"\n                                    },\n                                    {\n                                        name: \"使用文档\",\n                                        href: \"#docs\"\n                                    }\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                        href: item.href,\n                                        className: \"block px-4 py-2 text-gray-300 hover:text-blue-400 hover:bg-white/10 transition-colors rounded-lg mx-2\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                    href: \"https://github.com/Mcxiaocaibug/neptunium-frontend\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"flex items-center space-x-1 px-4 py-2 text-gray-300 hover:text-blue-400 hover:bg-white/10 transition-colors rounded-lg mx-2\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Github, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n"));

/***/ })

});