/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=Calendar,Download,Eye,FileText,Image,Package!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Calendar,Download,Eye,FileText,Image,Package!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Download: () => (/* reexport safe */ _icons_download_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   FileText: () => (/* reexport safe */ _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Image: () => (/* reexport safe */ _icons_image_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/calendar.js */ \"./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_download_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/download.js */ \"./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_file_text_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/file-text.js */ \"./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _icons_image_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/image.js */ \"./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DYWxlbmRhcixEb3dubG9hZCxFeWUsRmlsZVRleHQsSW1hZ2UsUGFja2FnZSE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUN5RDtBQUNBO0FBQ1Y7QUFDVztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmVwdHVuaXVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/MDc1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsZW5kYXIgfSBmcm9tIFwiLi9pY29ucy9jYWxlbmRhci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvd25sb2FkIH0gZnJvbSBcIi4vaWNvbnMvZG93bmxvYWQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeWUgfSBmcm9tIFwiLi9pY29ucy9leWUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBGaWxlVGV4dCB9IGZyb20gXCIuL2ljb25zL2ZpbGUtdGV4dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEltYWdlIH0gZnJvbSBcIi4vaWNvbnMvaW1hZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQYWNrYWdlIH0gZnJvbSBcIi4vaWNvbnMvcGFja2FnZS5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Calendar,Download,Eye,FileText,Image,Package!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=CheckCircle,Loader,Upload!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckCircle,Loader,Upload!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Loader: () => (/* reexport safe */ _icons_loader_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Upload: () => (/* reexport safe */ _icons_upload_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_loader_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/loader.js */ \"./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _icons_upload_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/upload.js */ \"./node_modules/lucide-react/dist/esm/icons/upload.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVja0NpcmNsZSxMb2FkZXIsVXBsb2FkIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ2dFO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXB0dW5pdW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9mNTMyIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NoZWNrLWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvYWRlciB9IGZyb20gXCIuL2ljb25zL2xvYWRlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVwbG9hZCB9IGZyb20gXCIuL2ljb25zL3VwbG9hZC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CheckCircle,Loader,Upload!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ExternalLink,Github,Heart!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ExternalLink,Github,Heart!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalLink: () => (/* reexport safe */ _icons_external_link_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Heart: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_external_link_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/external-link.js */ \"./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/github.js */ \"./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/heart.js */ \"./node_modules/lucide-react/dist/esm/icons/heart.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeHRlcm5hbExpbmssR2l0aHViLEhlYXJ0IT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ2tFO0FBQ2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXB0dW5pdW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9iODc0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFeHRlcm5hbExpbmsgfSBmcm9tIFwiLi9pY29ucy9leHRlcm5hbC1saW5rLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2l0aHViIH0gZnJvbSBcIi4vaWNvbnMvZ2l0aHViLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSGVhcnQgfSBmcm9tIFwiLi9pY29ucy9oZWFydC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ExternalLink,Github,Heart!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ExternalLink,Github,Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ExternalLink,Github,Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExternalLink: () => (/* reexport safe */ _icons_external_link_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_external_link_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/external-link.js */ \"./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/github.js */ \"./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeHRlcm5hbExpbmssR2l0aHViLE1lbnUsWCE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQ2tFO0FBQ2I7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL25lcHR1bml1bS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2QyN2MiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV4dGVybmFsTGluayB9IGZyb20gXCIuL2ljb25zL2V4dGVybmFsLWxpbmsuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBHaXRodWIgfSBmcm9tIFwiLi9pY29ucy9naXRodWIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZW51IH0gZnJvbSBcIi4vaWNvbnMvbWVudS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ExternalLink,Github,Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/FileUpload.tsx":
/*!***********************************!*\
  !*** ./components/FileUpload.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"react-dropzone\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader,Upload!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Loader,Upload!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_dropzone__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_4__, framer_motion__WEBPACK_IMPORTED_MODULE_5__]);\n([react_dropzone__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_4__, framer_motion__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction FileUpload({ onUploadSuccess }) {\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadedFile, setUploadedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (acceptedFiles)=>{\n        if (acceptedFiles.length === 0) return;\n        const file = acceptedFiles[0];\n        // 验证文件类型\n        const allowedTypes = [\n            \"litematic\",\n            \"schematic\",\n            \"schem\",\n            \"nbt\",\n            \"png\",\n            \"jpg\",\n            \"jpeg\"\n        ];\n        const fileExtension = file.name.split(\".\").pop()?.toLowerCase();\n        if (!fileExtension || !allowedTypes.includes(fileExtension)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(`不支持的文件类型: ${fileExtension}`);\n            return;\n        }\n        // 验证文件大小 (50MB)\n        const maxSize = 50 * 1024 * 1024;\n        if (file.size > maxSize) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(`文件过大: ${(file.size / 1024 / 1024).toFixed(2)}MB，最大允许50MB`);\n            return;\n        }\n        setUploading(true);\n        setUploadedFile(null);\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            // 调用API代理\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"/api/upload\", formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                timeout: 60000\n            });\n            if (response.data.success) {\n                setUploadedFile(response.data.data || null);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"文件上传成功！\");\n                onUploadSuccess?.();\n            } else {\n                throw new Error(response.data.error?.message || \"上传失败\");\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            const errorMessage = error.response?.data?.error?.message || error.message || \"上传失败\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(errorMessage);\n        } finally{\n            setUploading(false);\n        }\n    }, [\n        onUploadSuccess\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: {\n            \"application/octet-stream\": [\n                \".litematic\",\n                \".schematic\",\n                \".schem\",\n                \".nbt\"\n            ],\n            \"image/*\": [\n                \".png\",\n                \".jpg\",\n                \".jpeg\"\n            ]\n        },\n        maxFiles: 1,\n        disabled: uploading\n    });\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"已复制到剪贴板\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                ...getRootProps(),\n                className: `\n          border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300 relative overflow-hidden\n          ${isDragActive ? \"border-blue-400 bg-blue-500/10 backdrop-blur-sm\" : \"border-white/30 hover:border-blue-400/50 hover:bg-white/5\"}\n          ${uploading ? \"opacity-50 cursor-not-allowed\" : \"\"}\n        `,\n                whileHover: {\n                    scale: 1.02\n                },\n                whileTap: {\n                    scale: 0.98\n                },\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ...getInputProps()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10\",\n                        animate: {\n                            opacity: isDragActive ? 1 : 0,\n                            scale: isDragActive ? 1 : 0.8\n                        },\n                        transition: {\n                            duration: 0.3\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                animate: {\n                                    rotate: uploading ? 360 : 0,\n                                    scale: isDragActive ? 1.1 : 1\n                                },\n                                transition: {\n                                    rotate: {\n                                        duration: 2,\n                                        repeat: uploading ? Infinity : 0,\n                                        ease: \"linear\"\n                                    },\n                                    scale: {\n                                        duration: 0.2\n                                    }\n                                },\n                                children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Loader, {\n                                    className: \"mx-auto h-12 w-12 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Upload, {\n                                    className: \"mx-auto h-12 w-12 text-white/70\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: uploading ? \"正在上传文件...\" : isDragActive ? \"放开以上传文件\" : \"拖拽文件到此处或点击选择\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-300 mt-2\",\n                                        children: \"支持 .litematic, .schematic, .schem, .nbt, .png, .jpg 文件\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"最大文件大小: 50MB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                children: uploadedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"backdrop-blur-xl bg-green-500/10 border border-green-400/30 rounded-xl p-6\",\n                    initial: {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -50,\n                        scale: 0.9\n                    },\n                    transition: {\n                        duration: 0.5,\n                        type: \"spring\",\n                        bounce: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    scale: 0\n                                },\n                                animate: {\n                                    scale: 1\n                                },\n                                transition: {\n                                    delay: 0.2,\n                                    type: \"spring\",\n                                    bounce: 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__.CheckCircle, {\n                                    className: \"h-6 w-6 text-green-400 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h3, {\n                                        className: \"text-lg font-medium text-green-300 mb-3\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: 0.3\n                                        },\n                                        children: \"上传成功！\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"space-y-3\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.5\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: \"投影ID:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"bg-white/10 backdrop-blur-sm px-2 py-1 rounded text-blue-300 font-mono border border-white/20\",\n                                                                        children: uploadedFile.projectionId\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                        onClick: ()=>copyToClipboard(uploadedFile.projectionId),\n                                                                        className: \"text-blue-300 hover:text-blue-200 text-xs px-2 py-1 rounded bg-white/10 hover:bg-white/20 transition-all duration-200\",\n                                                                        whileHover: {\n                                                                            scale: 1.05\n                                                                        },\n                                                                        whileTap: {\n                                                                            scale: 0.95\n                                                                        },\n                                                                        children: \"复制\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.6\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: \"文件名:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mt-1\",\n                                                                children: uploadedFile.fileName\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.7\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: \"文件类型:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mt-1\",\n                                                                children: uploadedFile.fileType\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.8\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: \"文件大小:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mt-1\",\n                                                                children: [\n                                                                    (uploadedFile.fileSize / 1024 / 1024).toFixed(2),\n                                                                    \" MB\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                className: \"pt-3 border-t border-green-400/30\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.9\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: \"下载链接:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: uploadedFile.downloadUrl,\n                                                                readOnly: true,\n                                                                className: \"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded px-3 py-1 text-sm text-gray-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                onClick: ()=>copyToClipboard(uploadedFile.downloadUrl),\n                                                                className: \"bg-blue-500/80 backdrop-blur-sm text-white px-3 py-1 rounded text-sm hover:bg-blue-500 transition-all duration-200\",\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                children: \"复制链接\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                className: \"bg-blue-500/10 backdrop-blur-sm border border-blue-400/30 rounded-lg p-3 mt-4\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: 1.0\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-blue-300 mb-2\",\n                                                        children: \"\\uD83C\\uDFAE 游戏内使用方法:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-200 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"1. 在游戏中输入命令: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"bg-blue-400/20 px-1 rounded text-blue-100\",\n                                                                        children: [\n                                                                            \"/np get \",\n                                                                            uploadedFile.projectionId\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 39\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"2. 系统会自动下载并加载投影文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"3. 使用Litematica或WorldEdit查看和放置建筑\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/FileUpload.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/FileUpload.tsx\n");

/***/ }),

/***/ "./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Heart!=!lucide-react */ \"__barrel_optimize__?names=ExternalLink,Github,Heart!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.footer, {\n        className: \"backdrop-blur-xl bg-white/5 border-t border-white/10 text-white mt-16\",\n        initial: {\n            opacity: 0,\n            y: 50\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            className: \"text-2xl\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                rotate: 5\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                stiffness: 300\n                                            },\n                                            children: \"\\uD83C\\uDFD7️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                            children: \"Neptunium\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm mb-4\",\n                                    children: \"为Minecraft玩家打造的建筑投影系统，让创意在游戏中轻松实现。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    className: \"flex items-center space-x-1 text-sm text-gray-400\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Made with\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.2,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 2,\n                                                repeat: Infinity\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Heart, {\n                                                className: \"h-4 w-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for Minecraft community\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4 text-white\",\n                                    children: \"快速链接\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"文件上传\",\n                                                href: \"#upload\"\n                                            },\n                                            {\n                                                name: \"投影列表\",\n                                                href: \"#projections\"\n                                            },\n                                            {\n                                                name: \"使用文档\",\n                                                href: \"#docs\"\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.li, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.6 + index * 0.1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                                                    href: item.href,\n                                                    className: \"text-gray-300 hover:text-blue-400 transition-colors\",\n                                                    whileHover: {\n                                                        x: 5\n                                                    },\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, item.name, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.li, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: 0.9\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                                                href: \"https://github.com/Mcxiaocaibug/neptunium-frontend/issues\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"flex items-center space-x-1 text-gray-300 hover:text-blue-400 transition-colors\",\n                                                whileHover: {\n                                                    x: 5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"问题反馈\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__.ExternalLink, {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4 text-white\",\n                                    children: \"技术栈\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-300\",\n                                    children: [\n                                        \"Next.js + React\",\n                                        \"Tailwind CSS\",\n                                        \"Framer Motion\",\n                                        \"n8n 工作流\",\n                                        \"Cloudflare R2\",\n                                        \"Supabase PostgreSQL\",\n                                        \"Upstash Redis\"\n                                    ].map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.li, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: 0.8 + index * 0.1\n                                            },\n                                            whileHover: {\n                                                x: 5,\n                                                color: \"#60a5fa\"\n                                            },\n                                            children: [\n                                                \"• \",\n                                                tech\n                                            ]\n                                        }, tech, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    className: \"border-t border-white/10 mt-8 pt-6\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 1.0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                className: \"text-sm text-gray-400 mb-4 md:mb-0\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.2\n                                },\n                                children: \"\\xa9 2024 Neptunium. 开源项目，遵循 MIT 许可证。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                className: \"flex items-center space-x-4\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                                        href: \"https://github.com/Mcxiaocaibug/neptunium-frontend\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_2__.Github, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"GitHub\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"|\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                        className: \"text-sm text-gray-400\",\n                                        whileHover: {\n                                            color: \"#60a5fa\"\n                                        },\n                                        children: \"Version 1.0.0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Footer.tsx\n");

/***/ }),

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Menu,X!=!lucide-react */ \"__barrel_optimize__?names=ExternalLink,Github,Menu,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n        className: \"fixed top-0 left-0 right-0 backdrop-blur-xl bg-white/5 border-b border-white/10 z-50\",\n        initial: {\n            opacity: 0,\n            y: -50\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"flex items-center space-x-2\",\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"text-2xl\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        type: \"spring\",\n                                        stiffness: 300\n                                    },\n                                    children: \"\\uD83C\\uDFD7️\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                            children: \"Neptunium\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Minecraft投影系统\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.nav, {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            children: [\n                                [\n                                    {\n                                        name: \"上传文件\",\n                                        href: \"#upload\"\n                                    },\n                                    {\n                                        name: \"投影列表\",\n                                        href: \"#projections\"\n                                    },\n                                    {\n                                        name: \"使用文档\",\n                                        href: \"#docs\"\n                                    }\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                        href: item.href,\n                                        className: \"text-gray-300 hover:text-blue-400 transition-colors px-3 py-2 rounded-lg hover:bg-white/10\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6 + index * 0.1\n                                        },\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                    href: \"https://github.com/Mcxiaocaibug/neptunium-frontend\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-blue-400 transition-colors px-3 py-2 rounded-lg hover:bg-white/10\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    initial: {\n                                        opacity: 0,\n                                        y: -10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.9\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Github, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 text-gray-300 hover:text-blue-400 transition-colors rounded-lg hover:bg-white/10\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 27\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Menu, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 55\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                    children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"md:hidden border-t border-white/10 backdrop-blur-xl bg-white/5\",\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"py-4 space-y-2\",\n                            children: [\n                                [\n                                    {\n                                        name: \"上传文件\",\n                                        href: \"#upload\"\n                                    },\n                                    {\n                                        name: \"投影列表\",\n                                        href: \"#projections\"\n                                    },\n                                    {\n                                        name: \"使用文档\",\n                                        href: \"#docs\"\n                                    }\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                        href: item.href,\n                                        className: \"block px-4 py-2 text-gray-300 hover:text-blue-400 hover:bg-white/10 transition-colors rounded-lg mx-2\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        initial: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                    href: \"https://github.com/Mcxiaocaibug/neptunium-frontend\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"flex items-center space-x-1 px-4 py-2 text-gray-300 hover:text-blue-400 hover:bg-white/10 transition-colors rounded-lg mx-2\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Github, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.ExternalLink, {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/Header.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n");

/***/ }),

/***/ "./components/ProjectionList.tsx":
/*!***************************************!*\
  !*** ./components/ProjectionList.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Eye,FileText,Image,Package!=!lucide-react */ \"__barrel_optimize__?names=Calendar,Download,Eye,FileText,Image,Package!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, framer_motion__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction ProjectionList() {\n    const [projections, setProjections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchProjections();\n    }, []);\n    const fetchProjections = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // 这里应该调用获取投影列表的API\n            // 暂时使用模拟数据\n            const mockData = [\n                {\n                    projectionId: \"123456\",\n                    fileName: \"castle.litematic\",\n                    fileType: \"litematic\",\n                    fileSize: 2048576,\n                    uploadTime: \"2024-08-03T12:00:00Z\",\n                    downloadUrl: \"https://example.com/download/123456\",\n                    status: \"uploaded\"\n                },\n                {\n                    projectionId: \"789012\",\n                    fileName: \"house.schematic\",\n                    fileType: \"schematic\",\n                    fileSize: 1024000,\n                    uploadTime: \"2024-08-03T11:30:00Z\",\n                    downloadUrl: \"https://example.com/download/789012\",\n                    status: \"parsed\"\n                }\n            ];\n            setProjections(mockData);\n        } catch (error) {\n            console.error(\"Failed to fetch projections:\", error);\n            setError(\"获取投影列表失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getFileIcon = (fileType)=>{\n        switch(fileType.toLowerCase()){\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, this);\n            case \"litematic\":\n            case \"schematic\":\n            case \"schem\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Package, {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.FileText, {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            uploaded: {\n                color: \"bg-blue-500/20 text-blue-300 border-blue-400/30\",\n                text: \"已上传\"\n            },\n            parsing: {\n                color: \"bg-yellow-500/20 text-yellow-300 border-yellow-400/30\",\n                text: \"解析中\"\n            },\n            parsed: {\n                color: \"bg-green-500/20 text-green-300 border-green-400/30\",\n                text: \"已解析\"\n            },\n            parse_failed: {\n                color: \"bg-red-500/20 text-red-300 border-red-400/30\",\n                text: \"解析失败\"\n            },\n            deleted: {\n                color: \"bg-gray-500/20 text-gray-300 border-gray-400/30\",\n                text: \"已删除\"\n            }\n        };\n        const config = statusConfig[status] || statusConfig.uploaded;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium backdrop-blur-sm border ${config.color}`,\n            children: config.text\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 B\";\n        const k = 1024;\n        const sizes = [\n            \"B\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"zh-CN\", {\n            year: \"numeric\",\n            month: \"2-digit\",\n            day: \"2-digit\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const copyProjectionId = (id)=>{\n        navigator.clipboard.writeText(id);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"投影ID已复制到剪贴板\");\n    };\n    const downloadFile = (projection)=>{\n        window.open(projection.downloadUrl, \"_blank\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"开始下载文件\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"flex justify-center items-center py-12\",\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"rounded-full h-8 w-8 border-2 border-blue-400 border-t-transparent\",\n                    animate: {\n                        rotate: 360\n                    },\n                    transition: {\n                        duration: 1,\n                        repeat: Infinity,\n                        ease: \"linear\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-300\",\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"text-center py-12\",\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-400 mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                    onClick: fetchProjections,\n                    className: \"bg-blue-500/80 backdrop-blur-sm text-white px-4 py-2 rounded hover:bg-blue-500 transition-all duration-200\",\n                    whileHover: {\n                        scale: 1.05\n                    },\n                    whileTap: {\n                        scale: 0.95\n                    },\n                    children: \"重试\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    if (projections.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"text-center py-12\",\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        delay: 0.2,\n                        type: \"spring\",\n                        bounce: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Package, {\n                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                    className: \"text-gray-300 text-lg\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.4\n                    },\n                    children: \"暂无投影文件\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                    className: \"text-gray-400 text-sm mt-2\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.6\n                    },\n                    children: \"上传你的第一个建筑文件吧！\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"space-y-4\",\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n            children: projections.map((projection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"backdrop-blur-xl bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-300 border border-white/10\",\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -50\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: index * 0.1\n                    },\n                    whileHover: {\n                        scale: 1.02\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 flex-1\",\n                                    children: [\n                                        getFileIcon(projection.fileType),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-white truncate\",\n                                                            children: projection.fileName\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                            initial: {\n                                                                scale: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                delay: 0.2 + index * 0.1\n                                                            },\n                                                            children: getStatusBadge(projection.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-xs text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Calendar, {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                formatDate(projection.uploadTime)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: formatFileSize(projection.fileSize)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono bg-white/10 backdrop-blur-sm px-2 py-0.5 rounded border border-white/20 text-blue-300\",\n                                                            children: [\n                                                                \"ID: \",\n                                                                projection.projectionId\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: ()=>copyProjectionId(projection.projectionId),\n                                            className: \"p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-lg hover:bg-white/10\",\n                                            title: \"复制投影ID\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Eye, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: ()=>downloadFile(projection),\n                                            className: \"p-2 text-gray-400 hover:text-green-400 transition-colors rounded-lg hover:bg-white/10\",\n                                            title: \"下载文件\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Eye_FileText_Image_Package_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Download, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"mt-3 p-3 bg-blue-500/10 backdrop-blur-sm rounded-md border border-blue-400/30\",\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.3 + index * 0.1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-blue-300\",\n                                children: [\n                                    \"\\uD83C\\uDFAE 游戏内使用: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-blue-400/20 px-1 rounded text-blue-200\",\n                                        children: [\n                                            \"/np get \",\n                                            projection.projectionId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 27\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, projection.projectionId, true, {\n                    fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/components/ProjectionList.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ProjectionList.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/_app.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4ade80\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/FileUpload */ \"./components/FileUpload.tsx\");\n/* harmony import */ var _components_ProjectionList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ProjectionList */ \"./components/ProjectionList.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Header */ \"./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/Footer */ \"./components/Footer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__, _components_FileUpload__WEBPACK_IMPORTED_MODULE_4__, _components_ProjectionList__WEBPACK_IMPORTED_MODULE_5__, _components_Header__WEBPACK_IMPORTED_MODULE_6__, _components_Footer__WEBPACK_IMPORTED_MODULE_7__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_3__, _components_FileUpload__WEBPACK_IMPORTED_MODULE_4__, _components_ProjectionList__WEBPACK_IMPORTED_MODULE_5__, _components_Header__WEBPACK_IMPORTED_MODULE_6__, _components_Footer__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction Home() {\n    const [refreshList, setRefreshList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const handleUploadSuccess = ()=>{\n        // 刷新投影列表\n        setRefreshList((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Neptunium - Minecraft投影系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Minecraft建筑投影上传和管理系统\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 pointer-events-none\",\n                        children: mounted && [\n                            ...Array(50)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white/20 rounded-full\",\n                                initial: {\n                                    x: Math.random() * ( false ? 0 : 1920),\n                                    y: Math.random() * ( false ? 0 : 1080)\n                                },\n                                animate: {\n                                    x: Math.random() * ( false ? 0 : 1920),\n                                    y: Math.random() * ( false ? 0 : 1080)\n                                },\n                                transition: {\n                                    duration: Math.random() * 20 + 10,\n                                    repeat: Infinity,\n                                    repeatType: \"reverse\"\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto px-4 pt-24 pb-8 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"text-center mb-12\",\n                                initial: {\n                                    opacity: 0,\n                                    y: -50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                        className: \"text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-6\",\n                                        initial: {\n                                            scale: 0.5\n                                        },\n                                        animate: {\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        children: \"\\uD83C\\uDFD7️ Neptunium\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                        className: \"text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8\",\n                                        initial: {\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            \"Minecraft建筑投影系统 - 让你的创意在游戏中绽放\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-400\",\n                                                children: \"支持多种建筑文件格式，一键部署到游戏世界\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"flex flex-wrap justify-center gap-4 text-sm\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        children: [\n                                            \"✨ 支持Litematica\",\n                                            \"\\uD83D\\uDD27 支持WorldEdit\",\n                                            \"\\uD83D\\uDDBC️ 支持图片预览\",\n                                            \"☁️ 云端存储\"\n                                        ].map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                                                className: \"backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-white hover:bg-white/20 transition-all duration-300\",\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    delay: 0.8 + index * 0.1\n                                                },\n                                                children: tag\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"backdrop-blur-xl bg-white/10 rounded-2xl border border-white/20 shadow-2xl p-8 hover:bg-white/15 transition-all duration-300\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -100\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        whileHover: {\n                                            scale: 1.02,\n                                            boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-semibold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-4xl\",\n                                                        children: \"�\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"上传建筑文件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                onUploadSuccess: handleUploadSuccess\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"backdrop-blur-xl bg-white/10 rounded-2xl border border-white/20 shadow-2xl p-8 hover:bg-white/15 transition-all duration-300\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 100\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.8\n                                        },\n                                        whileHover: {\n                                            scale: 1.02,\n                                            boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-semibold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-4xl\",\n                                                        children: \"\\uD83C\\uDFD7️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"我的投影\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectionList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, refreshList, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 100\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 1.0\n                                },\n                                children: [\n                                    {\n                                        icon: \"⚡\",\n                                        title: \"极速部署\",\n                                        desc: \"一键上传，秒级部署到游戏世界\"\n                                    },\n                                    {\n                                        icon: \"\\uD83D\\uDD12\",\n                                        title: \"安全可靠\",\n                                        desc: \"企业级安全保障，数据加密存储\"\n                                    },\n                                    {\n                                        icon: \"\\uD83C\\uDF10\",\n                                        title: \"云端同步\",\n                                        desc: \"多设备同步，随时随地管理投影\"\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"backdrop-blur-xl bg-white/5 rounded-xl border border-white/10 p-6 text-center hover:bg-white/10 transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 1.2 + index * 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-4\",\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: feature.desc\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Neptunium/vercel-frontend/pages/index.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-dropzone":
/*!*********************************!*\
  !*** external "react-dropzone" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-dropzone");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();