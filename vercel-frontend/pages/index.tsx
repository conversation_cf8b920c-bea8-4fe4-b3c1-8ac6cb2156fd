import { useState, useEffect } from 'react'
import Head from 'next/head'
import { motion } from 'framer-motion'
import FileUpload from '../components/FileUpload'
import ProjectionList from '../components/ProjectionList'
import Header from '../components/Header'
import Footer from '../components/Footer'

export default function Home() {
  const [refreshList, setRefreshList] = useState(0)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleUploadSuccess = () => {
    // 刷新投影列表
    setRefreshList(prev => prev + 1)
  }

  return (
    <>
      <Head>
        <title>Neptunium - Minecraft投影系统</title>
        <meta name="description" content="Minecraft建筑投影上传和管理系统" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        {/* Header */}
        <Header />

        {/* 动态背景粒子效果 */}
        <div className="absolute inset-0 pointer-events-none">
          {mounted && [...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full"
              initial={{
                x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1920),
                y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 1080),
              }}
              animate={{
                x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1920),
                y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 1080),
              }}
              transition={{
                duration: Math.random() * 20 + 10,
                repeat: Infinity,
                repeatType: "reverse",
              }}
            />
          ))}
        </div>

        <Header />

        <main className="container mx-auto px-4 pt-24 pb-8 relative z-10">
          {/* 标题区域 */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.h1
              className="text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-6"
              initial={{ scale: 0.5 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              🏗️ Neptunium
            </motion.h1>
            <motion.p
              className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Minecraft建筑投影系统 - 让你的创意在游戏中绽放
              <br />
              <span className="text-blue-400">支持多种建筑文件格式，一键部署到游戏世界</span>
            </motion.p>
            <motion.div
              className="flex flex-wrap justify-center gap-4 text-sm"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {['✨ 支持Litematica', '🔧 支持WorldEdit', '🖼️ 支持图片预览', '☁️ 云端存储'].map((tag, index) => (
                <motion.span
                  key={index}
                  className="backdrop-blur-sm bg-white/10 border border-white/20 px-4 py-2 rounded-full text-white hover:bg-white/20 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                >
                  {tag}
                </motion.span>
              ))}
            </motion.div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* 文件上传区域 */}
            <motion.div
              className="backdrop-blur-xl bg-white/10 rounded-2xl border border-white/20 shadow-2xl p-8 hover:bg-white/15 transition-all duration-300"
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              whileHover={{
                scale: 1.02,
                boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)"
              }}
            >
              <h2 className="text-3xl font-semibold text-white mb-6 flex items-center">
                <span className="mr-3 text-4xl">�</span>
                上传建筑文件
              </h2>
              <FileUpload onUploadSuccess={handleUploadSuccess} />
            </motion.div>

            {/* 投影列表区域 */}
            <motion.div
              className="backdrop-blur-xl bg-white/10 rounded-2xl border border-white/20 shadow-2xl p-8 hover:bg-white/15 transition-all duration-300"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              whileHover={{
                scale: 1.02,
                boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)"
              }}
            >
              <h2 className="text-3xl font-semibold text-white mb-6 flex items-center">
                <span className="mr-3 text-4xl">🏗️</span>
                我的投影
              </h2>
              <ProjectionList key={refreshList} />
            </motion.div>
          </div>

          {/* 特性展示区域 */}
          <motion.div
            className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            {[
              { icon: "⚡", title: "极速部署", desc: "一键上传，秒级部署到游戏世界" },
              { icon: "🔒", title: "安全可靠", desc: "企业级安全保障，数据加密存储" },
              { icon: "🌐", title: "云端同步", desc: "多设备同步，随时随地管理投影" }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="backdrop-blur-xl bg-white/5 rounded-xl border border-white/10 p-6 text-center hover:bg-white/10 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 + index * 0.2 }}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                <p className="text-gray-300">{feature.desc}</p>
              </motion.div>
            ))}
          </motion.div>
        </main>

        <Footer />
      </div>
    </>
  )
}
